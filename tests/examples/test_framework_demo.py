"""
Demonstration of the new legoAgent testing framework.

This module shows how to use the different test types and utilities
provided by the enhanced testing framework.
"""

import asyncio
import pytest
from unittest.mock import patch

from tests.base import (
    UnitTestCase,
    IntegrationTestCase,
    FunctionalTestCase,
    PerformanceTestCase,
)
from tests.mocks.services import MockLLMService, MockServiceManager
from tests.mocks.factories import LLMServiceRequestFactory, ChainWorkflowRequestFactory

from lego_agent.core.di import dependency_registry
from lego_agent.core.services_manager import BaseServiceManager
from lego_agent.agents.anthropic.patterns.workflow.chaining import chain, chain_async


@pytest.mark.unit
class TestFrameworkDemoUnit(UnitTestCase):
    """Demonstration of unit testing with the new framework."""

    def test_mock_service_basic_usage(self):
        """Demonstrate basic mock service usage."""
        # Create a mock service with predefined responses
        service = MockLLMService(
            responses=["Hello, World!", "How can I help you?"],
            delay=0.0,  # No delay for unit tests
        )

        # Create a test request using factory
        request = LLMServiceRequestFactory()

        # Test the service
        response = service.serve(request)

        # Verify the response
        assert response is not None
        assert response.service_name == "llm"
        assert service.call_count == 1

        # Test call history tracking
        assert len(service.call_history) == 1
        assert service.last_request == request

    def test_factory_generated_data(self):
        """Demonstrate test data generation with factories."""
        # Generate multiple requests
        requests = [LLMServiceRequestFactory() for _ in range(3)]

        # Verify each request has required fields
        for request in requests:
            assert hasattr(request, "service_name")
            assert hasattr(request, "operation_name")
            assert hasattr(request, "raw_data")
            assert "messages" in request.raw_data

    def test_performance_tracking(self, performance_tracker):
        """Demonstrate performance tracking in unit tests."""
        service = MockLLMService(responses=["Fast response"], delay=0.001)
        request = LLMServiceRequestFactory()

        # Track operation performance
        performance_tracker.start_timer("unit_test_operation")
        response = service.serve(request)
        performance_tracker.end_timer("unit_test_operation")

        # Verify performance
        assert response is not None
        performance_tracker.assert_under_threshold("unit_test_operation", 0.1)

    def test_service_reset_functionality(self):
        """Demonstrate service reset between tests."""
        service = MockLLMService(responses=["Test"])

        # Make some requests
        for _ in range(3):
            service.serve(LLMServiceRequestFactory())

        assert service.call_count == 3

        # Reset the service
        service.reset()

        # Verify reset
        assert service.call_count == 0
        assert service.last_request is None
        assert len(service.call_history) == 0


@pytest.mark.integration
class TestFrameworkDemoIntegration(IntegrationTestCase):
    """Demonstration of integration testing with the new framework."""

    def setup_method(self):
        """Setup integration test environment."""
        super().setup_method()
        self._setup_test_services()

    def _setup_test_services(self):
        """Setup services for integration testing."""
        # Create mock services
        self.llm_service = MockLLMService(
            responses=["Integration response 1", "Integration response 2"],
            delay=0.01,  # Small delay to simulate real service
        )

        # Create service manager
        self.service_manager = MockServiceManager()
        self.service_manager.add_mock_service("llm", self.llm_service)

        # Register in dependency registry
        dependency_registry.register(BaseServiceManager, self.service_manager)

    def test_service_manager_integration(self):
        """Demonstrate service manager integration testing."""
        # Get service through manager
        llm = self.service_manager.get_service("llm")

        # Verify it's the same service
        assert llm is self.llm_service

        # Test request through manager
        request = LLMServiceRequestFactory()
        response = llm.serve(request)

        assert response is not None
        assert self.llm_service.call_count == 1

    @pytest.mark.asyncio
    async def test_async_service_integration(self):
        """Demonstrate async integration testing."""
        llm = self.service_manager.get_service("llm")

        # Test async request
        request = LLMServiceRequestFactory()
        response = await llm.serve_async(request)

        assert response is not None
        assert self.llm_service.call_count == 1

    def test_multiple_service_coordination(self):
        """Demonstrate coordination between multiple services."""
        # This would typically involve multiple real services
        # For demo, we'll show the pattern with one service

        llm = self.service_manager.get_service("llm")

        # Simulate a workflow that uses the service multiple times
        requests = [LLMServiceRequestFactory() for _ in range(3)]
        responses = []

        for request in requests:
            response = llm.serve(request)
            responses.append(response)

        # Verify all requests were processed
        assert len(responses) == 3
        assert self.llm_service.call_count == 3


@pytest.mark.functional
class TestFrameworkDemoFunctional(FunctionalTestCase):
    """Demonstration of functional/end-to-end testing."""

    def setup_method(self):
        """Setup functional test environment."""
        super().setup_method()
        self._setup_test_environment()

    def _setup_test_environment(self):
        """Setup realistic test environment."""
        # Create service with realistic responses for chain workflow
        self.llm_service = MockLLMService(
            responses=[
                "Step 1: Analyzing the input data...",
                "Step 2: Processing the analysis results...",
                "Step 3: Generating final recommendations...",
            ],
            delay=0.05,  # Realistic response time
        )

        # Setup service manager
        self.service_manager = MockServiceManager()
        self.service_manager.add_mock_service("llm", self.llm_service)
        dependency_registry.register(BaseServiceManager, self.service_manager)

    def test_complete_chain_workflow(self):
        """Demonstrate complete workflow testing."""
        # Create a realistic workflow request
        request = ChainWorkflowRequestFactory()

        # Execute the complete workflow
        response = chain(request)

        # Verify end-to-end functionality
        assert response is not None
        assert response.output is not None
        assert len(response.steps) == len(request.prompts)

        # Verify service interactions
        assert self.llm_service.call_count == len(request.prompts)

        # Verify workflow progression
        for i, step in enumerate(response.steps):
            assert step is not None
            assert f"Step {i+1}" in step

    @pytest.mark.asyncio
    async def test_async_workflow_end_to_end(self):
        """Demonstrate async end-to-end workflow testing."""
        request = ChainWorkflowRequestFactory()

        # Execute async workflow
        response = await chain_async(request)

        # Verify async completion
        assert response is not None
        assert len(response.steps) == len(request.prompts)

    def test_workflow_error_scenarios(self):
        """Demonstrate error scenario testing."""
        # Create a service that will fail on the second call
        failing_service = MockLLMService(responses=["Success"], delay=0.0)

        call_count = 0
        original_serve = failing_service.serve

        def failing_serve(request):
            nonlocal call_count
            call_count += 1
            if call_count > 1:
                raise Exception("Simulated service failure")
            return original_serve(request)

        failing_service.serve = failing_serve
        self.service_manager.add_mock_service("llm", failing_service)

        # Test error handling
        request = ChainWorkflowRequestFactory()

        with pytest.raises(Exception, match="Simulated service failure"):
            chain(request)


@pytest.mark.performance
class TestFrameworkDemoPerformance(PerformanceTestCase):
    """Demonstration of performance testing."""

    def setup_method(self):
        """Setup performance test environment."""
        super().setup_method()

        # Create high-performance mock service
        self.llm_service = MockLLMService(
            responses=["Performance test response"] * 1000,
            delay=0.001,  # Minimal delay for performance testing
        )

        self.service_manager = MockServiceManager()
        self.service_manager.add_mock_service("llm", self.llm_service)
        dependency_registry.register(BaseServiceManager, self.service_manager)

    @pytest.mark.benchmark
    def test_service_throughput_benchmark(self, benchmark):
        """Demonstrate benchmarking with pytest-benchmark."""

        def make_request():
            request = LLMServiceRequestFactory()
            return self.llm_service.serve(request)

        # Benchmark the function
        result = benchmark(make_request)

        # Verify result and performance
        assert result is not None
        assert benchmark.stats.mean < 0.01  # Less than 10ms average

    def test_load_testing(self):
        """Demonstrate load testing capabilities."""
        num_requests = 100

        # Record start time
        self.record_operation_time("load_test_start", 0.0)

        # Perform load test
        start_time = self.metrics["start_time"]

        for i in range(num_requests):
            request = LLMServiceRequestFactory()
            response = self.llm_service.serve(request)
            assert response is not None

        # Record completion
        import time

        total_time = time.time() - start_time
        self.record_operation_time("load_test_complete", total_time)

        # Verify performance
        assert self.llm_service.call_count == num_requests
        self.assert_operation_under_threshold("load_test_complete", 2.0)

        # Calculate throughput
        throughput = num_requests / total_time
        assert throughput > 50  # At least 50 requests per second

    def test_memory_usage_monitoring(self):
        """Demonstrate memory usage monitoring."""
        import psutil
        import os

        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB

        # Perform memory-intensive operations
        for i in range(1000):
            request = LLMServiceRequestFactory()
            response = self.llm_service.serve(request)

        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory

        # Record memory metrics
        self.metrics["memory_usage"]["initial"] = initial_memory
        self.metrics["memory_usage"]["final"] = final_memory
        self.metrics["memory_usage"]["increase"] = memory_increase

        # Verify reasonable memory usage
        assert memory_increase < 50  # Less than 50MB increase


# Example of using fixtures from the framework
def test_using_common_fixtures(sample_text_data, sample_embeddings, test_config_data):
    """Demonstrate using common fixtures."""
    # Use sample text data
    assert len(sample_text_data) > 0
    assert all(isinstance(text, str) for text in sample_text_data)

    # Use sample embeddings
    assert len(sample_embeddings) > 0
    assert all(len(embedding) > 300 for embedding in sample_embeddings)

    # Use test configuration
    assert "anthropic.chain" in test_config_data
    assert "model" in test_config_data["anthropic.chain"]


def test_using_service_fixtures(mock_llm_service, mock_service_manager):
    """Demonstrate using service fixtures."""
    # Use mock LLM service
    request = LLMServiceRequestFactory()
    response = mock_llm_service.serve(request)
    assert response is not None

    # Use mock service manager
    llm = mock_service_manager.get_service("llm")
    assert llm is not None
