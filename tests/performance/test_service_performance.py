"""
Performance tests for legoAgent services.

This module contains benchmarks and performance tests for core services.
"""

import asyncio
import time
from concurrent.futures import ThreadPool<PERSON>xecutor, as_completed
import pytest

from tests.base import PerformanceTestCase
from tests.mocks.services import (
    MockLLMService,
    MockEmbeddingService,
    MockVectorDBService,
)
from tests.mocks.factories import (
    LLMServiceRequestFactory,
    EmbeddingServiceRequestFactory,
)

from lego_agent.core.di import dependency_registry
from lego_agent.core.services_manager import BaseServiceManager


@pytest.mark.performance
class TestServicePerformance(PerformanceTestCase):
    """Performance tests for individual services."""

    def setup_method(self):
        """Setup for service performance tests."""
        super().setup_method()
        self._setup_performance_monitoring()

        # Create high-performance mock services
        self.llm_service = MockLLMService(
            responses=["Performance test response"] * 1000,
            delay=0.001,  # Minimal delay for performance testing
        )
        self.embedding_service = MockEmbeddingService(embedding_dim=384, delay=0.001)
        self.vdb_service = MockVectorDBService(delay=0.001)

    @pytest.mark.benchmark
    def test_llm_service_throughput(self, benchmark):
        """Benchmark LLM service throughput."""

        def llm_request():
            request = LLMServiceRequestFactory()
            return self.llm_service.serve(request)

        # Benchmark the function
        result = benchmark(llm_request)
        assert result is not None

        # Verify performance characteristics
        assert benchmark.stats.mean < 0.01  # Less than 10ms average

    @pytest.mark.benchmark
    def test_embedding_service_throughput(self, benchmark):
        """Benchmark embedding service throughput."""

        def embedding_request():
            request = EmbeddingServiceRequestFactory()
            return self.embedding_service.serve(request)

        result = benchmark(embedding_request)
        assert result is not None
        assert benchmark.stats.mean < 0.01

    def test_llm_service_concurrent_load(self):
        """Test LLM service under concurrent load."""
        num_requests = 100
        num_workers = 10

        def make_request():
            request = LLMServiceRequestFactory()
            start_time = time.time()
            response = self.llm_service.serve(request)
            end_time = time.time()
            return end_time - start_time, response

        start_time = time.time()

        with ThreadPoolExecutor(max_workers=num_workers) as executor:
            futures = [executor.submit(make_request) for _ in range(num_requests)]
            results = [future.result() for future in as_completed(futures)]

        total_time = time.time() - start_time

        # Verify all requests completed
        assert len(results) == num_requests
        assert all(result[1] is not None for result in results)

        # Calculate performance metrics
        response_times = [result[0] for result in results]
        avg_response_time = sum(response_times) / len(response_times)
        throughput = num_requests / total_time

        # Performance assertions
        assert avg_response_time < 0.1  # Average response time under 100ms
        assert throughput > 50  # At least 50 requests per second
        assert self.llm_service.call_count == num_requests

        # Record metrics
        self.record_operation_time("concurrent_llm_load", total_time)
        self.assert_operation_under_threshold("concurrent_llm_load", 5.0)

    @pytest.mark.asyncio
    async def test_async_service_performance(self):
        """Test async service performance."""
        num_requests = 50

        async def make_async_request():
            request = LLMServiceRequestFactory()
            start_time = time.time()
            response = await self.llm_service.serve_async(request)
            end_time = time.time()
            return end_time - start_time, response

        start_time = time.time()

        # Create concurrent async tasks
        tasks = [make_async_request() for _ in range(num_requests)]
        results = await asyncio.gather(*tasks)

        total_time = time.time() - start_time

        # Verify results
        assert len(results) == num_requests
        assert all(result[1] is not None for result in results)

        # Calculate async performance metrics
        response_times = [result[0] for result in results]
        avg_response_time = sum(response_times) / len(response_times)
        throughput = num_requests / total_time

        # Async should be more efficient
        assert avg_response_time < 0.05  # Even better response time for async
        assert throughput > 100  # Higher throughput for async

        self.record_operation_time("async_service_load", total_time)
        self.assert_operation_under_threshold("async_service_load", 2.0)

    def test_embedding_service_batch_performance(self):
        """Test embedding service performance with batch processing."""
        batch_sizes = [1, 5, 10, 20, 50]

        for batch_size in batch_sizes:
            texts = [f"Test text {i}" for i in range(batch_size)]

            start_time = time.time()

            request = EmbeddingServiceRequestFactory()
            request.raw_data["texts"] = texts
            response = self.embedding_service.serve(request)

            end_time = time.time()
            processing_time = end_time - start_time

            # Verify response
            assert len(response.raw_data["embeddings"]) == batch_size

            # Record performance for different batch sizes
            self.record_operation_time(f"embedding_batch_{batch_size}", processing_time)

            # Larger batches should be more efficient per item
            time_per_item = processing_time / batch_size
            assert time_per_item < 0.01  # Less than 10ms per item

    def test_vdb_service_search_performance(self):
        """Test vector database search performance."""
        # Test different search parameters
        k_values = [1, 5, 10, 20]
        num_queries = [1, 5, 10]

        for k in k_values:
            for num_query in num_queries:
                query_embeddings = [[0.1] * 384 for _ in range(num_query)]

                start_time = time.time()

                request = {
                    "service_name": "vdb",
                    "operation_name": "search",
                    "raw_data": {"query_embeddings": query_embeddings, "k": k},
                }

                from lego_agent.core.service.vdb import VectorDBServiceRequest

                vdb_request = VectorDBServiceRequest(**request)
                response = self.vdb_service.serve(vdb_request)

                end_time = time.time()
                search_time = end_time - start_time

                # Verify response structure
                assert len(response.raw_data["results"]) == num_query

                # Record performance metrics
                operation_name = f"vdb_search_k{k}_q{num_query}"
                self.record_operation_time(operation_name, search_time)

                # Performance assertions
                assert search_time < 0.1  # Search should be fast

    def test_memory_usage_under_load(self):
        """Test memory usage under sustained load."""
        import psutil
        import os

        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB

        # Sustained load test
        num_iterations = 1000

        for i in range(num_iterations):
            # Mix of different service calls
            llm_request = LLMServiceRequestFactory()
            embedding_request = EmbeddingServiceRequestFactory()

            self.llm_service.serve(llm_request)
            self.embedding_service.serve(embedding_request)

            # Check memory every 100 iterations
            if i % 100 == 0:
                current_memory = process.memory_info().rss / 1024 / 1024
                memory_increase = current_memory - initial_memory

                # Memory shouldn't grow excessively
                assert memory_increase < 100  # Less than 100MB increase

        final_memory = process.memory_info().rss / 1024 / 1024
        total_memory_increase = final_memory - initial_memory

        # Record final memory metrics
        self.metrics["memory_usage"]["initial"] = initial_memory
        self.metrics["memory_usage"]["final"] = final_memory
        self.metrics["memory_usage"]["increase"] = total_memory_increase

        # Memory usage should be reasonable
        assert total_memory_increase < 50  # Less than 50MB total increase


@pytest.mark.performance
class TestWorkflowPerformance(PerformanceTestCase):
    """Performance tests for workflows."""

    def setup_method(self):
        """Setup for workflow performance tests."""
        super().setup_method()

        # Setup high-performance services
        from tests.mocks.services import MockServiceManager

        self.llm_service = MockLLMService(
            responses=["Fast response"] * 10000, delay=0.001
        )

        self.service_manager = MockServiceManager()
        self.service_manager.add_mock_service("llm", self.llm_service)
        dependency_registry.register(BaseServiceManager, self.service_manager)

    @pytest.mark.benchmark
    def test_chain_workflow_performance(self, benchmark, chain_workflow_data):
        """Benchmark chain workflow performance."""
        from lego_agent.agents.anthropic.patterns.workflow.chaining import chain

        request = chain_workflow_data["short_chain"]

        def run_chain():
            return chain(request)

        result = benchmark(run_chain)
        assert result is not None
        assert benchmark.stats.mean < 0.1  # Less than 100ms

    @pytest.mark.benchmark
    def test_parallel_workflow_performance(self, benchmark, parallel_workflow_data):
        """Benchmark parallel workflow performance."""
        from lego_agent.agents.anthropic.patterns.workflow.paralleling import parallel

        request = parallel_workflow_data["sentiment_analysis"]
        request.n_workers = 3  # Optimize for performance

        def run_parallel():
            return parallel(request)

        result = benchmark(run_parallel)
        assert result is not None
        assert benchmark.stats.mean < 0.2  # Less than 200ms

    def test_workflow_scalability(self):
        """Test workflow scalability with increasing load."""
        from lego_agent.agents.anthropic.patterns.workflow.paralleling import (
            parallel,
            ParallelWorkflowRequest,
        )

        input_sizes = [5, 10, 20, 50]

        for size in input_sizes:
            request = ParallelWorkflowRequest(
                prompt="Process this input:",
                inputs=[f"Input {i}" for i in range(size)],
                n_workers=min(size, 10),  # Cap workers at 10
                model="test-model",
            )

            start_time = time.time()
            response = parallel(request)
            end_time = time.time()

            processing_time = end_time - start_time

            # Verify response
            assert len(response.outputs) == size

            # Record scalability metrics
            self.record_operation_time(f"parallel_scale_{size}", processing_time)

            # Time should scale reasonably
            time_per_input = processing_time / size
            assert time_per_input < 0.05  # Less than 50ms per input
