"""
Centralized dependency registry setup.
This module initializes all service registrations in one place.
"""

from .di import dependency_registry
from .service.llm import LLMService
from .service.vdb import VectorDBService
from .service.embedding import EmbeddingService
from .services_manager import BaseServiceManager
from .config import Configuration<PERSON>anager, config_manager
from .factories import (
    create_llm_service,
    create_vdb_service,
    create_service_manager,
    create_embedding_service,
)
import logging

logger = logging.getLogger(__name__)


def setup_registry():
    """Set up all service registrations."""
    try:
        # Register configuration manager
        dependency_registry.register(ConfigurationManager, config_manager)

        # Register core services
        dependency_registry.register_factory(LLMService, create_llm_service)
        dependency_registry.register_factory(VectorDBService, create_vdb_service)
        dependency_registry.register_factory(BaseServiceManager, create_service_manager)
        dependency_registry.register_factory(EmbeddingService, create_embedding_service)

        logger.info("Dependency registry setup completed successfully")
    except Exception as e:
        logger.error(f"Failed to setup dependency registry: {str(e)}", exc_info=True)
        raise


# Convenience functions for service resolution
def get_llm_service() -> LLMService:
    """Get or create an LLM service instance."""
    return dependency_registry.resolve(LLMService)


def get_service_manager() -> BaseServiceManager:
    """Get or create a service manager instance."""
    return dependency_registry.resolve(BaseServiceManager)


def get_config_manager() -> ConfigurationManager:
    """Get the configuration manager instance."""
    return dependency_registry.resolve(ConfigurationManager)


def get_vdb_service() -> VectorDBService:
    """Get or create a VectorDB service instance."""
    return dependency_registry.resolve(VectorDBService)


def get_embedding_service() -> EmbeddingService:
    """Get or create an embedding service instance."""
    return dependency_registry.resolve(EmbeddingService)
